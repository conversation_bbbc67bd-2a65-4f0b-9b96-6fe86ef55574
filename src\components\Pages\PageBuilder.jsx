import React, { useState, useEffect } from "react";
import Sidebar from "../Layout/Sidebar";
import Header from "../Layout/Header";
import DragDropBuilder from "./DragDropBuilder";
import {
  Plus,
  FileText,
  Edit2,
  Trash2,
  Eye,
  Search,
  Filter,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";

const PageBuilder = () => {
  const [pages, setPages] = useState([]);
  const [showBuilder, setShowBuilder] = useState(false);
  const [editingPage, setEditingPage] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const api = useHttp();

  useEffect(() => {
    // Fetch pages
    api.sendRequest(CONSTANTS.API.pages.get, (res) => {
      console.log("Pages fetched:", res);
      setPages(res);
    });
  }, []);

  const refreshPages = () => {
    api.sendRequest(CONSTANTS.API.pages.get, (res) => {
      setPages(res);
    });
  };

  const handleEdit = (page) => {
    setEditingPage(page);
    setShowBuilder(true);
  };

  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete this page?")) {
      api.sendRequest(
        apiGenerator(CONSTANTS.API.pages.delete, { id }),
        (res) => {
          console.log("Page deleted successfully:", res);
          refreshPages();
        },
        null,
        "Page deleted successfully!"
      );
    }
  };

  const handleSave = async () => {
    refreshPages();
    setShowBuilder(false);
    setEditingPage(null);
  };

  const filteredPages = pages.filter(
    (page) =>
      page.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.slug.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (api.isLoading) {
    return (
      <div className="">
        <div className="tw-animate-pulse tw-p-6">
          <div className="tw-h-8 tw-bg-gray-300 tw-rounded tw-mb-4"></div>
          <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-lg:tw-grid-cols-3 tw-gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="tw-h-48 tw-bg-gray-300 tw-rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (showBuilder) {
    return (
      <div className="">
        <DragDropBuilder
          page={editingPage}
          onSave={handleSave}
          onCancel={() => {
            setShowBuilder(false);
            setEditingPage(null);
          }}
        />
      </div>
    );
  }

  return (
    <div className="tw-p-6">
      <div className="tw-flex tw-flex-col tw-lg:tw-flex-row tw-justify-between tw-items-start tw-lg:tw-items-center tw-mb-6 tw-space-y-4 tw-lg:tw-space-y-0">
        <div className="tw-flex tw-items-center tw-justify-between tw-w-full">
          <div className="tw-flex tw-items-center">
            <FileText className="tw-w-6 tw-h-6 tw-text-blue-600 tw-mr-2" />
            <h2 className="tw-text-xl tw-font-semibold tw-text-gray-900">
              Pages ({filteredPages.length})
            </h2>
          </div>
          <button
            onClick={() => setShowBuilder(true)}
            className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-4 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-justify-center tw-whitespace-nowrap"
          >
            <Plus className="tw-w-4 tw-h-4 tw-mr-2" />
            Create Page
          </button>
        </div>

        <div className="tw-flex tw-flex-col tw-sm:tw-flex-row tw-space-y-2 tw-sm:tw-space-y-0 tw-sm:tw-space-x-4 tw-w-full tw-lg:tw-w-auto">
          {/* Search */}
          <div className="tw-relative">
            <Search className="tw-absolute tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-w-4 tw-h-4 tw-text-gray-400" />
            <input
              type="text"
              placeholder="Search pages..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="tw-pl-10 tw-pr-4 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-w-full tw-sm:tw-w-64"
            />
          </div>
        </div>
      </div>

      {/* Pages Grid */}
      <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-lg:tw-grid-cols-3 tw-gap-6">
        {filteredPages.map((page) => (
          <div
            key={page.id}
            className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-hover:tw-shadow-md tw-transition-all tw-duration-200"
          >
            <div className="tw-p-6">
              <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
                <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-truncate">
                  {page.name}
                </h3>

                <div className="tw-flex tw-space-x-2">
                  <button
                    onClick={() => handleEdit(page)}
                    className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-blue-600 tw-hover:tw-bg-blue-50 tw-rounded-lg tw-transition-colors"
                  >
                    <Edit2 className="tw-w-4 tw-h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(page.id)}
                    className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-red-600 tw-hover:tw-bg-red-50 tw-rounded-lg tw-transition-colors"
                  >
                    <Trash2 className="tw-w-4 tw-h-4" />
                  </button>
                </div>
              </div>

              <div className="tw-mb-4">
                <p className="tw-text-sm tw-text-gray-600 tw-mb-2">
                  <strong>Slug:</strong> /{page.slug}
                </p>
                {page.meta_title && (
                  <p className="tw-text-sm tw-text-gray-600 tw-mb-2">
                    <strong>Meta Title:</strong> {page.meta_title}
                  </p>
                )}
                {page.meta_description && (
                  <p className="tw-text-sm tw-text-gray-600 tw-truncate">
                    <strong>Description:</strong> {page.meta_description}
                  </p>
                )}
              </div>

              <div className="tw-mb-4">
                <p className="tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Components: {page.components ? page.components.length : 0}
                </p>

                {page.components && page.components.length > 0 && (
                  <div className="tw-bg-gray-50 tw-rounded-lg tw-p-3">
                    <div className="tw-text-xs tw-text-gray-600">
                      {page.components.slice(0, 3).map((comp, index) => (
                        <div
                          key={index}
                          className="tw-flex tw-items-center tw-mb-1"
                        >
                          <div className="tw-w-2 tw-h-2 tw-bg-blue-500 tw-rounded-full tw-mr-2"></div>
                          Component #{comp.id}
                        </div>
                      ))}
                      {page.components.length > 3 && (
                        <div className="tw-text-gray-500 tw-mt-1">
                          +{page.components.length - 3} more components
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div className="tw-flex tw-justify-between tw-items-center tw-text-xs tw-text-gray-500">
                <span>v{page.version}</span>
                <span>{new Date(page.created_at).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredPages.length === 0 && (
        <div className="tw-text-center tw-py-12">
          <FileText className="tw-w-16 tw-h-16 tw-text-gray-300 tw-mx-auto tw-mb-4" />
          <h3 className="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-2">
            {searchTerm ? "No pages found" : "No pages yet"}
          </h3>
          <p className="tw-text-gray-500 tw-mb-4">
            {searchTerm
              ? "Try adjusting your search criteria"
              : "Create your first page using the drag-and-drop builder"}
          </p>
        </div>
      )}
    </div>
  );
};

export default PageBuilder;
